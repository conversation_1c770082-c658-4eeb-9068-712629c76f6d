<script setup lang="ts">
import { onMounted, onUpdated, ref, computed } from 'vue';
import hljs from 'highlight.js';
import { useDrawerDirection } from '@/composables/useDrawerDirection';
import ContentRenderer from './ContentRenderer.vue';
// import 'highlight.js/styles/foundation.css';

const drawerVisible = ref(false);
const note = ref('');

// 使用动态方向
const { drawerDirection } = useDrawerDirection();

// 预处理 note 内容，使用 hljs 对代码块进行高亮处理
const processedNote = computed(() => {
  if (!note.value) return '';

  // 使用正则表达式匹配 <pre><code> 标签
  const codeBlockRegex = /<pre><code(?:\s+class="([^"]*)")?[^>]*>([\s\S]*?)<\/code><\/pre>/g;

  return note.value.replace(codeBlockRegex, (match, className, codeContent) => {
    try {
      // 解码 HTML 实体
      const decodedContent = codeContent
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&amp;/g, '&')
        .replace(/&quot;/g, '"')
        .replace(/&#39;/g, "'");

      // 使用 hljs 进行语法高亮
      const highlightResult = hljs.highlightAuto(decodedContent);

      // 构建高亮后的代码块
      const highlightedCode = highlightResult.value;
      const detectedLanguage = highlightResult.language || '';

      // 保持原有的 class 属性，并添加 hljs 相关类
      let classAttr = 'hljs';
      if (className) {
        classAttr = `${className} hljs`;
      }
      if (detectedLanguage) {
        classAttr += ` language-${detectedLanguage}`;
      }

      return `<pre><code class="${classAttr}">${highlightedCode}</code></pre>`;
    } catch (error) {
      console.warn('代码高亮处理失败:', error);
      // 如果处理失败，返回原始内容
      return match;
    }
  });
});

// 展示drawer
const showDrawer = (data: any) => {
  drawerVisible.value = true;
  note.value = data;
};

defineExpose({
  showDrawer
});
</script>
<template>
  <el-drawer
    v-model="drawerVisible"
    :direction="drawerDirection"
    size="740px"
    class="drawer"
  >
    <template #header>
      <div class="header-text">
        编者笔记
        <div style="margin-top: 5px" class="line"></div>
      </div>
    </template>

    <div class="wrapper NoteDrawer">
      <span v-if="!note">暂无内容</span>
      <span v-else>
        <ContentRenderer :content="processedNote"></ContentRenderer>
      </span>
    </div>
  </el-drawer>
</template>
<style scoped>
.header-text {
  color: var(--color-black);
  font-weight: 600;
  /* word-break: break-all; */
  position: relative;
}
.wrapper {
  font-size: 14px;
  color: var(--color-black);
}
.line {
  width: 140%;
  height: 1px;
  background-color: var(--color-boxborder);
  position: absolute;
  bottom: -10px;
  left: -20px;
}

/* 移除 el-drawer 内部的默认 padding */
:deep(.el-drawer) {
  padding: 0 !important; 
  overflow-y: auto !important;
  
}
&::-webkit-scrollbar-thumb {
    background-color: #c1c1c1;
    border-radius: 6px;
  }

:deep(.el-drawer__header) {
  padding: 20px 20px 0 20px;
  margin: 0;
}
</style>
